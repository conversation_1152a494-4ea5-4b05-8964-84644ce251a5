# Talent API Environment Variables Setup Summary

## Changes Made

### 1. Created New Terraform Modules

#### Storage Module (`infrastructure/modules/storage/`)
- **Purpose**: Manages GCS buckets for file storage
- **Key Features**:
  - Creates buckets with lifecycle policies
  - Configures CORS for web uploads
  - Sets up IAM permissions for service accounts
  - Supports versioning and uniform bucket-level access

#### Secrets Module (`infrastructure/modules/secrets/`)
- **Purpose**: Manages Secret Manager secrets
- **Key Features**:
  - Creates secrets with optional initial values
  - Sets up IAM permissions for service account access
  - Supports automatic secret rotation

### 2. Updated Main Infrastructure (`infrastructure/dev/main.tf`)

#### Added New Resources:
- **Storage Module**: Creates `main-resumes` GCS bucket
- **Secrets Module**: Creates secrets for sensitive environment variables
- **Null Resource**: Automatically constructs and sets DATABASE_URL secret

#### Updated Talent API Cloud Run Service:
- **Environment Variables**: Added all required non-sensitive variables
- **Secret Variables**: Added all sensitive variables from Secret Manager
- **IAM Roles**: Updated to include `storage.objectAdmin` for bucket access
- **Dependencies**: Added proper dependency chain

#### Updated APIs:
- Added `secretmanager.googleapis.com`
- Added `storage.googleapis.com`

### 3. Updated Pub/Sub Configuration
- Modified Pub/Sub module to support exact topic names
- Configured topics with specific names:
  - `cerana.matches.compute.v1`
  - `cerana.talent.resume_uploaded.v1`
  - `cerana.talent.profile_updated.v1`

### 4. Environment Variables Configuration

#### Regular Environment Variables (Cloud Run):
```
ENVIRONMENT=development
GCP_PROJECT_ID={project_id}
GCS_BUCKET_RESUMES={bucket_name}
PUBSUB_TOPIC_MATCHES_COMPUTE=cerana.matches.compute.v1
PUBSUB_TOPIC_RESUME_UPLOADED=cerana.talent.resume_uploaded.v1
PUBSUB_TOPIC_PROFILE_UPDATED=cerana.talent.profile_updated.v1
EMBEDDINGS_PROVIDER=vertex
VECTOR_DIM=3072
CORS_ALLOW_ORIGINS=["*"]
RATE_LIMIT_REQUESTS_PER_MINUTE=60
MAX_RESUME_SIZE_MB=25
MATCH_MIN_SKILL_OVERLAP=1
MATCH_TOP_K_RESULTS=50
LOG_LEVEL=INFO
LOG_FORMAT=json
```

#### Secret Environment Variables (Secret Manager):
```
DATABASE_URL=postgresql+asyncpg://app_user:{password}@{host}:5432/{db_name}
CLERK_JWKS_URL=https://exact-horse-66.clerk.accounts.dev/.well-known/jwks.json
CLERK_ISSUER=https://exact-horse-66.clerk.accounts.dev
CLERK_AUDIENCE=https://cerana.netlify.app
PUBSUB_VERIFICATION_TOKEN={random_string}
```

## Deployment Steps

### 1. Deploy Infrastructure
```bash
cd infrastructure/dev
terraform init
terraform plan
terraform apply
```

### 2. Verify Setup
```bash
# Check Cloud Run service
gcloud run services describe talent-api --region=us-central1

# Check secrets
gcloud secrets list --filter="name:main-"

# Check buckets
gsutil ls -p {project_id}

# Check Pub/Sub topics
gcloud pubsub topics list --filter="name:cerana"
```

## Key Benefits

1. **Security**: Sensitive data stored in Secret Manager
2. **Automation**: DATABASE_URL automatically constructed
3. **Scalability**: Modular design for easy replication
4. **Compliance**: Proper IAM roles and least-privilege access
5. **Maintainability**: Clear separation of concerns

## Next Steps

1. Deploy the infrastructure
2. Test the talent-api service
3. Monitor logs and metrics
4. Set up alerting and monitoring

## Troubleshooting

If you encounter issues:

1. **Check Terraform state**: `terraform state list`
2. **Verify API enablement**: `gcloud services list --enabled`
3. **Check IAM permissions**: `gcloud projects get-iam-policy {project_id}`
4. **Review Cloud Run logs**: `gcloud run services logs read talent-api`
5. **Validate secrets**: `gcloud secrets versions access latest --secret={secret_name}`

## Files Modified/Created

### New Files:
- `infrastructure/modules/storage/main.tf`
- `infrastructure/modules/storage/variables.tf`
- `infrastructure/modules/storage/outputs.tf`
- `infrastructure/modules/secrets/main.tf`
- `infrastructure/modules/secrets/variables.tf`
- `infrastructure/modules/secrets/outputs.tf`
- `infrastructure/TALENT_API_ENVIRONMENT_VARIABLES.md`
- `infrastructure/TALENT_API_SETUP_SUMMARY.md`

### Modified Files:
- `infrastructure/dev/main.tf`
- `infrastructure/dev/versions.tf`
- `infrastructure/dev/outputs.tf`
- `infrastructure/modules/pubsub/main.tf`
- `infrastructure/modules/pubsub/variables.tf`
