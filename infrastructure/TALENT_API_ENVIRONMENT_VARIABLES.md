# Talent API Environment Variables

This document describes the environment variables configuration for the talent-api Cloud Run service.

## Overview

The talent-api service requires several environment variables for proper operation. These are managed through Terraform and include both regular environment variables and sensitive secrets stored in Google Secret Manager.

## Environment Variables

### Regular Environment Variables (managed by Terraform)

These are set directly in the Cloud Run service configuration:

- `ENVIRONMENT`: The deployment environment ("development")
- `GCP_PROJECT_ID`: The GCP project ID
- `GCS_BUCKET_RESUMES`: Name of the GCS bucket for storing resume files
- `PUBSUB_TOPIC_MATCHES_COMPUTE`: Pub/Sub topic for match computation (cerana.matches.compute.v1)
- `PUBSUB_TOPIC_RESUME_UPLOADED`: Pub/Sub topic for resume upload events (cerana.talent.resume_uploaded.v1)
- `PUBSUB_TOPIC_PROFILE_UPDATED`: Pub/Sub topic for profile update events (cerana.talent.profile_updated.v1)
- `EMBEDDINGS_PROVIDER`: AI embeddings provider ("vertex")
- `VECTOR_DIM`: Vector dimension for embeddings (3072)
- `CORS_ALLOW_ORIGINS`: CORS allowed origins (["*"])
- `RATE_LIMIT_REQUESTS_PER_MINUTE`: Rate limiting (60)
- `MAX_RESUME_SIZE_MB`: Maximum resume file size (25)
- `MATCH_MIN_SKILL_OVERLAP`: Minimum skill overlap for matching (1)
- `MATCH_TOP_K_RESULTS`: Top K results for matching (50)
- `LOG_LEVEL`: Logging level ("INFO")
- `LOG_FORMAT`: Log format ("json")

### Secret Environment Variables (managed by Secret Manager)

These are stored securely in Google Secret Manager:

- `DATABASE_URL`: Complete database connection URL (postgresql+asyncpg://...)
- `CLERK_JWKS_URL`: Clerk JWKS URL for JWT verification
- `CLERK_ISSUER`: Clerk JWT issuer
- `CLERK_AUDIENCE`: Clerk JWT audience
- `PUBSUB_VERIFICATION_TOKEN`: Pub/Sub verification token
- `OPENAI_API_KEY`: OpenAI API key (temporary - will be removed when application uses Vertex AI)

## Database URL

The `DATABASE_URL` is automatically constructed and stored as a secret with the format:

```
postgresql+asyncpg://app_user:{password}@{host}:5432/{database_name}
```

Example:
```
postgresql+asyncpg://app_user:password123@********:5432/main_db
```

## Setup Instructions

### 1. Deploy Infrastructure

```bash
cd infrastructure/dev
terraform init
terraform plan
terraform apply
```

### 2. Update Secrets (if needed)

The DATABASE_URL is automatically set by Terraform, but you can update other secrets:

```bash
# Update Clerk JWKS URL (if different from default)
echo -n "https://exact-horse-66.clerk.accounts.dev/.well-known/jwks.json" | \
  gcloud secrets versions add main-clerk-jwks-url --data-file=-

# Update Clerk Issuer (if different from default)
echo -n "https://exact-horse-66.clerk.accounts.dev" | \
  gcloud secrets versions add main-clerk-issuer --data-file=-

# Update Clerk Audience (if different from default)
echo -n "https://cerana.netlify.app" | \
  gcloud secrets versions add main-clerk-audience --data-file=-

# Update Pub/Sub verification token
echo -n "your-secure-random-verification-token" | \
  gcloud secrets versions add main-pubsub-verification-token --data-file=-
```

### 4. Verify DATABASE_URL Secret

The DATABASE_URL is automatically constructed and set by Terraform. You can verify it was set correctly:

```bash
# Check if the DATABASE_URL secret exists and has a value
gcloud secrets versions access latest --secret="main-database-url"
```

## Verification

### Check Environment Variables

You can verify the environment variables are set correctly by checking the Cloud Run service:

```bash
gcloud run services describe talent-api --region=us-central1 --format="value(spec.template.spec.template.spec.containers[0].env[].name)"
```

### Check Secret Access

Verify that the service account has access to the secrets:

```bash
# Get the service account email
terraform output talent_api_cloud_run_service_account

# Check secret access
gcloud secrets get-iam-policy main-database-url
```

## Troubleshooting

### Secret Access Issues

If the application can't access secrets:

1. Verify the service account has the `secretmanager.secretAccessor` role
2. Check that the secret exists and has a version
3. Ensure the secret name matches what's configured in Terraform

### Database Connection Issues

If database connection fails:

1. Verify the CloudSQL instance is running
2. Check that the VPC connector is properly configured
3. Ensure the database user and password are correct

### Storage Access Issues

If GCS bucket access fails:

1. Verify the service account has `storage.objectAdmin` role
2. Check that the bucket exists and is in the correct region
3. Ensure the bucket name is correctly set in the environment variable

## Security Notes

- Never commit API keys or passwords to version control
- Use Secret Manager for all sensitive configuration
- Regularly rotate API keys and tokens
- Monitor secret access logs for unauthorized usage
- Use least-privilege IAM roles for service accounts
