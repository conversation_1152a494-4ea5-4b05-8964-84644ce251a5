#!/bin/bash

# Talent API Environment Variables Verification Script
# This script verifies that all required environment variables and resources are properly configured

set -e

echo "🔍 Verifying Talent API Setup..."
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Get project ID from terraform
echo "📋 Getting project information..."
cd infrastructure/dev
PROJECT_ID=$(terraform output -raw project_id 2>/dev/null || echo "")
REGION=$(terraform output -raw region 2>/dev/null || echo "us-central1")

if [ -z "$PROJECT_ID" ]; then
    echo -e "${RED}❌ Could not get project ID from terraform. Make sure terraform has been applied.${NC}"
    exit 1
fi

echo "Project ID: $PROJECT_ID"
echo "Region: $REGION"
echo ""

# 1. Check Cloud Run service
echo "🚀 Checking Cloud Run service..."
if gcloud run services describe talent-api --region=$REGION --project=$PROJECT_ID >/dev/null 2>&1; then
    print_status 0 "talent-api Cloud Run service exists"
    
    # Check environment variables
    echo "   Checking environment variables..."
    ENV_VARS=$(gcloud run services describe talent-api --region=$REGION --project=$PROJECT_ID --format="value(spec.template.spec.template.spec.containers[0].env[].name)" 2>/dev/null | tr '\n' ' ')
    
    REQUIRED_ENV_VARS=("ENVIRONMENT" "GCP_PROJECT_ID" "GCS_BUCKET_RESUMES" "PUBSUB_TOPIC_MATCHES_COMPUTE" "PUBSUB_TOPIC_RESUME_UPLOADED" "PUBSUB_TOPIC_PROFILE_UPDATED" "EMBEDDINGS_PROVIDER" "VECTOR_DIM" "CORS_ALLOW_ORIGINS" "RATE_LIMIT_REQUESTS_PER_MINUTE" "MAX_RESUME_SIZE_MB" "MATCH_MIN_SKILL_OVERLAP" "MATCH_TOP_K_RESULTS" "LOG_LEVEL" "LOG_FORMAT")
    
    for var in "${REQUIRED_ENV_VARS[@]}"; do
        if echo "$ENV_VARS" | grep -q "$var"; then
            print_status 0 "Environment variable $var is set"
        else
            print_status 1 "Environment variable $var is missing"
        fi
    done
    
    # Check secret environment variables
    echo "   Checking secret environment variables..."
    SECRET_ENV_VARS=$(gcloud run services describe talent-api --region=$REGION --project=$PROJECT_ID --format="value(spec.template.spec.template.spec.containers[0].env[].valueFrom.secretKeyRef.name)" 2>/dev/null | tr '\n' ' ')
    
    REQUIRED_SECRET_VARS=("main-database-url" "main-clerk-jwks-url" "main-clerk-issuer" "main-clerk-audience" "main-pubsub-verification-token" "main-openai-api-key")
    
    for var in "${REQUIRED_SECRET_VARS[@]}"; do
        if echo "$SECRET_ENV_VARS" | grep -q "$var"; then
            print_status 0 "Secret environment variable $var is configured"
        else
            print_status 1 "Secret environment variable $var is missing"
        fi
    done
else
    print_status 1 "talent-api Cloud Run service not found"
fi
echo ""

# 2. Check Secret Manager secrets
echo "🔐 Checking Secret Manager secrets..."
SECRETS=$(gcloud secrets list --project=$PROJECT_ID --filter="name:main-" --format="value(name)" 2>/dev/null)

REQUIRED_SECRETS=("main-database-url" "main-clerk-jwks-url" "main-clerk-issuer" "main-clerk-audience" "main-pubsub-verification-token" "main-openai-api-key")

for secret in "${REQUIRED_SECRETS[@]}"; do
    if echo "$SECRETS" | grep -q "$secret"; then
        print_status 0 "Secret $secret exists"
        
        # Check if secret has a value
        if gcloud secrets versions access latest --secret="$secret" --project=$PROJECT_ID >/dev/null 2>&1; then
            print_status 0 "Secret $secret has a value"
        else
            print_status 1 "Secret $secret has no value"
        fi
    else
        print_status 1 "Secret $secret not found"
    fi
done
echo ""

# 3. Check GCS buckets
echo "🪣 Checking GCS buckets..."
if gsutil ls -p $PROJECT_ID | grep -q "main-resumes"; then
    print_status 0 "GCS bucket main-resumes exists"
    
    # Check bucket permissions
    BUCKET_IAM=$(gsutil iam get gs://main-resumes 2>/dev/null || echo "")
    if echo "$BUCKET_IAM" | grep -q "storage.objectAdmin"; then
        print_status 0 "Bucket has proper IAM permissions"
    else
        print_status 1 "Bucket IAM permissions may be incorrect"
    fi
else
    print_status 1 "GCS bucket main-resumes not found"
fi
echo ""

# 4. Check Pub/Sub topics
echo "📢 Checking Pub/Sub topics..."
TOPICS=$(gcloud pubsub topics list --project=$PROJECT_ID --format="value(name)" 2>/dev/null)

REQUIRED_TOPICS=("cerana.matches.compute.v1" "cerana.talent.resume_uploaded.v1" "cerana.talent.profile_updated.v1")

for topic in "${REQUIRED_TOPICS[@]}"; do
    if echo "$TOPICS" | grep -q "$topic"; then
        print_status 0 "Pub/Sub topic $topic exists"
    else
        print_status 1 "Pub/Sub topic $topic not found"
    fi
done
echo ""

# 5. Check service account permissions
echo "👤 Checking service account permissions..."
SA_EMAIL=$(terraform output -raw talent_api_cloud_run_service_account 2>/dev/null || echo "")

if [ -n "$SA_EMAIL" ]; then
    print_status 0 "Service account: $SA_EMAIL"
    
    # Check IAM roles
    SA_ROLES=$(gcloud projects get-iam-policy $PROJECT_ID --flatten="bindings[].members" --format="table(bindings.role)" --filter="bindings.members:$SA_EMAIL" 2>/dev/null | tail -n +2)
    
    REQUIRED_ROLES=("roles/cloudsql.client" "roles/secretmanager.secretAccessor" "roles/storage.objectAdmin" "roles/pubsub.publisher" "roles/pubsub.subscriber" "roles/aiplatform.user")
    
    for role in "${REQUIRED_ROLES[@]}"; do
        if echo "$SA_ROLES" | grep -q "$role"; then
            print_status 0 "Service account has role $role"
        else
            print_status 1 "Service account missing role $role"
        fi
    done
else
    print_status 1 "Could not get service account email"
fi
echo ""

# 6. Summary
echo "📊 Summary"
echo "=========="
echo "If all checks passed, your talent-api service should be ready to use."
echo ""
echo "Next steps:"
echo "1. Test the service:"
echo "   curl https://talent-api-{hash}-{region}.a.run.app/health"
echo ""
echo "2. Check logs:"
echo "   gcloud run services logs read talent-api --region=$REGION"
echo ""
echo "For more information, see infrastructure/TALENT_API_ENVIRONMENT_VARIABLES.md"
