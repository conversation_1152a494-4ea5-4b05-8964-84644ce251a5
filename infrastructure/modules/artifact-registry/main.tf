# modules/artifact-registry/main.tf
resource "google_artifact_registry_repository" "repos" {
  for_each = var.repositories
  
  repository_id = "${var.environment}-${each.key}"
  format        = each.value.format
  location      = var.region
  description   = each.value.description
  
  labels = {
    environment = var.environment
    managed_by  = "terraform"
  }
  
  # Cleanup policy for Docker repositories
  dynamic "cleanup_policies" {
    for_each = each.value.format == "DOCKER" ? [1] : []
    content {
      id     = "keep-recent-versions"
      action = "KEEP"
      
      most_recent_versions {
        keep_count = 10
      }
    }
  }
}

# IAM for repositories
resource "google_artifact_registry_repository_iam_member" "readers" {
  for_each = var.repositories
  
  repository = google_artifact_registry_repository.repos[each.key].id
  location   = var.region
  role       = "roles/artifactregistry.reader"
  member     = "allUsers"  # Adjust for private repositories
}

# modules/artifact-registry/variables.tf
variable "project_id" {
  description = "GCP Project ID"
  type        = string
}

variable "region" {
  description = "GCP Region"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "repositories" {
  description = "Map of repositories to create"
  type = map(object({
    format      = string
    description = string
  }))
}