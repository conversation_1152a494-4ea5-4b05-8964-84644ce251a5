# modules/workload-identity/main.tf
resource "google_iam_workload_identity_pool" "github" {
  workload_identity_pool_id = "${var.environment}-github-pool"
  display_name              = "GitHub Actions Pool"
  description               = "Workload Identity Pool for GitHub Actions"
}

resource "google_iam_workload_identity_pool_provider" "github" {
  workload_identity_pool_id          = google_iam_workload_identity_pool.github.workload_identity_pool_id
  workload_identity_pool_provider_id = "${var.environment}-github-provider"
  display_name                        = "GitHub Actions Provider"
  
  attribute_mapping = {
    "google.subject"       = "assertion.sub"
    "attribute.actor"      = "assertion.actor"
    "attribute.repository" = "assertion.repository"
    "attribute.repository_owner" = "assertion.repository_owner"
  }
  
  attribute_condition = "assertion.repository_owner == '${var.github_org}'"
  
  oidc {
    issuer_uri = "https://token.actions.githubusercontent.com"
  }
}

# Service accounts for GitHub Actions (one per repository)
resource "google_service_account" "github_actions" {
  for_each = var.github_repositories

  account_id   = "${var.environment}-github-${each.key}"
  display_name = "GitHub Actions Service Account - ${each.value.name}"
  description  = "Service account for GitHub Actions deployments for ${each.value.name}"
}

# Allow GitHub Actions to impersonate the service accounts
resource "google_service_account_iam_member" "github_actions_workload_identity" {
  for_each = var.github_repositories

  service_account_id = google_service_account.github_actions[each.key].name
  role               = "roles/iam.workloadIdentityUser"
  member             = "principalSet://iam.googleapis.com/${google_iam_workload_identity_pool.github.name}/attribute.repository/${var.github_org}/${each.value.name}"
}

# Grant necessary permissions to the service accounts
resource "google_project_iam_member" "github_actions_roles" {
  for_each = {
    for combo in flatten([
      for repo_key, repo in var.github_repositories : [
        for role in repo.roles : {
          key  = "${repo_key}-${role}"
          repo = repo_key
          role = role
        }
      ]
    ]) : combo.key => combo
  }

  project = var.project_id
  role    = each.value.role
  member  = "serviceAccount:${google_service_account.github_actions[each.value.repo].email}"
}