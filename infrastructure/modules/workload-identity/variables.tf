# modules/workload-identity/variables.tf
variable "project_id" {
  description = "GCP Project ID"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "github_org" {
  description = "GitHub organization or username"
  type        = string
}

variable "github_repositories" {
  description = "Map of GitHub repository configurations"
  type = map(object({
    name = string
    roles = optional(list(string), [
      "roles/artifactregistry.writer",
      "roles/run.developer",
      "roles/container.developer",
      "roles/storage.objectAdmin"
    ])
  }))
}