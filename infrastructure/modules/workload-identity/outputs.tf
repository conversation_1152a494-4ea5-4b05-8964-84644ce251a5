# modules/workload-identity/outputs.tf
output "provider_name" {
  value = "projects/${var.project_id}/locations/global/workloadIdentityPools/${google_iam_workload_identity_pool.github.workload_identity_pool_id}/providers/${google_iam_workload_identity_pool_provider.github.workload_identity_pool_provider_id}"
}

output "service_account_emails" {
  value = {
    for key, sa in google_service_account.github_actions : key => sa.email
  }
}

output "pool_id" {
  value = google_iam_workload_identity_pool.github.workload_identity_pool_id
}