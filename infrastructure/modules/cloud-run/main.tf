# modules/cloud-run/main.tf - Hybrid approach
# Terraform manages the service, GitHub Actions updates the image

# This module now requires an existing service account to be passed in
# Service account creation and IAM management should be done outside this module

# Allow GitHub Actions service accounts to impersonate this Cloud Run service account
resource "google_service_account_iam_member" "github_actions_impersonation" {
  for_each = toset(var.github_actions_service_accounts)

  service_account_id = "projects/${var.project_id}/serviceAccounts/${var.existing_service_account_email}"
  role               = "roles/iam.serviceAccountUser"
  member             = "serviceAccount:${each.value}"
}

# Cloud Run Service - Managed by Terraform, image updated by GitHub Actions
resource "google_cloud_run_v2_service" "main" {
  name     = "${var.environment}-${var.service_name}"
  location = var.region
  
  template {
    service_account = var.existing_service_account_email
    execution_environment = var.execution_environment

    annotations = var.annotations

    labels = merge(
      var.labels,
      {
        environment = var.environment
        managed_by  = "terraform"
      }
    )
    
    containers {
      # Initial image - will be overridden by GitHub Actions
      image = var.initial_image
      
      ports {
        container_port = var.port
        name          = "http1"
      }
      
      # Environment variables managed by Terraform
      dynamic "env" {
        for_each = var.env_vars
        content {
          name  = env.value.name
          value = env.value.value
        }
      }
      
      # Environment variables from secrets (with version)
      dynamic "env" {
        for_each = [for env in var.secret_env_vars : env if env.version != null]
        content {
          name = env.value.name
          value_source {
            secret_key_ref {
              secret = env.value.secret_name
              version = env.value.version
            }
          }
        }
      }

      # Environment variables from secrets (without version - uses latest)
      dynamic "env" {
        for_each = [for env in var.secret_env_vars : env if env.version == null]
        content {
          name = env.value.name
          value_source {
            secret_key_ref {
              secret = env.value.secret_name
            }
          }
        }
      }
      
      resources {
        limits = {
          cpu    = var.cpu
          memory = var.memory
        }
        
        cpu_idle          = var.cpu_idle
        startup_cpu_boost = var.startup_cpu_boost
      }
      
      # Startup probe - more lenient settings
      dynamic "startup_probe" {
        for_each = var.health_check_path != null ? [1] : []
        content {
          http_get {
            path = var.health_check_path
            port = var.port
          }
          initial_delay_seconds = 10
          timeout_seconds       = 10
          period_seconds        = 30
          failure_threshold     = 10
        }
      }
      
      # Liveness probe - more lenient settings
      dynamic "liveness_probe" {
        for_each = var.health_check_path != null ? [1] : []
        content {
          http_get {
            path = var.health_check_path
            port = var.port
          }
          period_seconds    = 30
          timeout_seconds   = 10
          failure_threshold = 5
        }
      }

      # CloudSQL volume mounts
      dynamic "volume_mounts" {
        for_each = length(var.cloudsql_connections) > 0 ? [1] : []
        content {
          name       = "cloudsql"
          mount_path = "/cloudsql"
        }
      }
    }
    
    scaling {
      min_instance_count = var.min_instances
      max_instance_count = var.max_instances
    }
    
    # VPC connector
    dynamic "vpc_access" {
      for_each = var.vpc_connector_name != "" ? [1] : []
      content {
        connector = var.vpc_connector_name
        egress    = var.vpc_egress
      }
    }

    # CloudSQL connections
    dynamic "volumes" {
      for_each = length(var.cloudsql_connections) > 0 ? [1] : []
      content {
        name = "cloudsql"
        cloud_sql_instance {
          instances = var.cloudsql_connections
        }
      }
    }
    
    session_affinity = var.session_affinity
    timeout         = var.timeout
    max_instance_request_concurrency = var.max_instance_request_concurrency
  }
  
  traffic {
    type    = "TRAFFIC_TARGET_ALLOCATION_TYPE_LATEST"
    percent = 100
  }
  
  # CRITICAL: Ignore changes made by GitHub Actions
  # This allows GitHub Actions to deploy new images without Terraform reverting them
  lifecycle {
    ignore_changes = [
      # Container image and revision changes from deployments
      template[0].containers[0].image,
      template[0].revision,
      latest_created_revision,

      # Labels that GitHub Actions may set
      template[0].labels["commit-sha"],
      template[0].labels["deployed-by"],
      template[0].labels["managed-by"],

      # Annotations that GitHub Actions may set
      template[0].annotations["github.run-id"],

      # Client information from gcloud deployments
      client,
      client_version,
    ]
  }

  # Service account and IAM permissions are managed outside this module
}

# IAM for public access
resource "google_cloud_run_service_iam_member" "public" {
  count = var.allow_unauthenticated ? 1 : 0
  
  service  = google_cloud_run_v2_service.main.name
  location = var.region
  role     = "roles/run.invoker"
  member   = "allUsers"
}
