# modules/pubsub/outputs.tf
output "topic_names" {
  description = "Map of topic names"
  value = {
    for k, v in google_pubsub_topic.topics : k => v.name
  }
}

output "topic_ids" {
  description = "Map of topic full resource IDs"
  value = {
    for k, v in google_pubsub_topic.topics : k => v.id
  }
}

output "subscription_names" {
  description = "Map of subscription names"
  value = {
    for k, v in google_pubsub_subscription.subscriptions : k => v.name
  }
}

output "subscription_ids" {
  description = "Map of subscription full resource IDs"
  value = {
    for k, v in google_pubsub_subscription.subscriptions : k => v.id
  }
}

output "dead_letter_topic_names" {
  description = "Map of dead letter topic names"
  value = {
    for k, v in google_pubsub_topic.dead_letter : k => v.name
  }
}