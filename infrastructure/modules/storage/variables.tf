# modules/storage/variables.tf

variable "project_id" {
  description = "GCP Project ID"
  type        = string
}

variable "region" {
  description = "GCP Region"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "buckets" {
  description = "Map of GCS buckets to create"
  type = map(object({
    location      = string
    storage_class = optional(string, "STANDARD")
    description   = optional(string, "")
    versioning    = optional(bool, false)
    public_read   = optional(bool, false)
    lifecycle_rules = optional(list(object({
      action = object({
        type          = string
        storage_class = optional(string)
      })
      condition = object({
        age                        = optional(number)
        created_before             = optional(string)
        with_state                 = optional(string)
        matches_storage_class      = optional(list(string))
        num_newer_versions         = optional(number)
        custom_time_before         = optional(string)
        days_since_custom_time     = optional(number)
        days_since_noncurrent_time = optional(number)
        noncurrent_time_before     = optional(string)
      })
    })), [])
  }))
}

variable "service_account_email" {
  description = "Service account email that needs access to the buckets"
  type        = string
  default     = ""
}
