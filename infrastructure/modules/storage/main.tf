# modules/storage/main.tf

# GCS Buckets
resource "google_storage_bucket" "buckets" {
  for_each = var.buckets
  
  name          = "${var.environment}-${each.key}"
  location      = each.value.location
  storage_class = each.value.storage_class
  
  # Prevent accidental deletion
  lifecycle {
    prevent_destroy = true
  }
  
  # Versioning
  dynamic "versioning" {
    for_each = each.value.versioning ? [1] : []
    content {
      enabled = true
    }
  }
  
  # Lifecycle rules
  dynamic "lifecycle_rule" {
    for_each = each.value.lifecycle_rules
    content {
      action {
        type          = lifecycle_rule.value.action.type
        storage_class = lookup(lifecycle_rule.value.action, "storage_class", null)
      }
      
      condition {
        age                        = lookup(lifecycle_rule.value.condition, "age", null)
        created_before             = lookup(lifecycle_rule.value.condition, "created_before", null)
        with_state                 = lookup(lifecycle_rule.value.condition, "with_state", null)
        matches_storage_class      = lookup(lifecycle_rule.value.condition, "matches_storage_class", null)
        num_newer_versions         = lookup(lifecycle_rule.value.condition, "num_newer_versions", null)
        custom_time_before         = lookup(lifecycle_rule.value.condition, "custom_time_before", null)
        days_since_custom_time     = lookup(lifecycle_rule.value.condition, "days_since_custom_time", null)
        days_since_noncurrent_time = lookup(lifecycle_rule.value.condition, "days_since_noncurrent_time", null)
        noncurrent_time_before     = lookup(lifecycle_rule.value.condition, "noncurrent_time_before", null)
      }
    }
  }
  
  # CORS configuration for web uploads
  cors {
    origin          = ["*"]
    method          = ["GET", "HEAD", "PUT", "POST", "DELETE"]
    response_header = ["*"]
    max_age_seconds = 3600
  }
  
  # Uniform bucket-level access
  uniform_bucket_level_access = true
  
  # Labels
  labels = {
    environment = var.environment
    managed_by  = "terraform"
    purpose     = each.key
  }
}

# IAM for bucket access
resource "google_storage_bucket_iam_member" "bucket_admins" {
  for_each = var.buckets
  
  bucket = google_storage_bucket.buckets[each.key].name
  role   = "roles/storage.objectAdmin"
  member = "serviceAccount:${var.service_account_email}"
  
  depends_on = [google_storage_bucket.buckets]
}

# Public read access for specific buckets if needed
resource "google_storage_bucket_iam_member" "public_read" {
  for_each = {
    for k, v in var.buckets : k => v
    if lookup(v, "public_read", false)
  }
  
  bucket = google_storage_bucket.buckets[each.key].name
  role   = "roles/storage.objectViewer"
  member = "allUsers"
  
  depends_on = [google_storage_bucket.buckets]
}
