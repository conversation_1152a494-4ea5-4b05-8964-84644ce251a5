# modules/secrets/variables.tf

variable "project_id" {
  description = "GCP Project ID"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "secrets" {
  description = "Map of secrets to create"
  type = map(object({
    description   = optional(string, "")
    initial_value = optional(string, null)
  }))
}

variable "service_account_email" {
  description = "Service account email that needs access to the secrets"
  type        = string
}
