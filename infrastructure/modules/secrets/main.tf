# modules/secrets/main.tf

# Secret Manager secrets
resource "google_secret_manager_secret" "secrets" {
  for_each = var.secrets
  
  secret_id = "${var.environment}-${each.key}"
  
  replication {
    auto {}
  }
  
  labels = {
    environment = var.environment
    managed_by  = "terraform"
    purpose     = each.key
  }
}

# Secret versions - only create if initial_value is provided
resource "google_secret_manager_secret_version" "secret_versions" {
  for_each = {
    for k, v in var.secrets : k => v
    if v.initial_value != null
  }
  
  secret      = google_secret_manager_secret.secrets[each.key].id
  secret_data = each.value.initial_value
  
  lifecycle {
    ignore_changes = [secret_data]
  }
}

# IAM for secret access
resource "google_secret_manager_secret_iam_member" "secret_accessors" {
  for_each = var.secrets
  
  secret_id = google_secret_manager_secret.secrets[each.key].secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${var.service_account_email}"
  
  depends_on = [google_secret_manager_secret.secrets]
}
