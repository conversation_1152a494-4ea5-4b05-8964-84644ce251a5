# modules/cloud-run/main.tf
resource "google_cloud_run_v2_service" "main" {
  name     = "${var.environment}-${var.service_name}"
  location = var.region
  
  template {
    service_account = google_service_account.cloud_run.email
    
    containers {
      image = var.image
      
      ports {
        container_port = var.port
      }
      
      env {
        name  = "ENVIRONMENT"
        value = var.environment
      }
      
      resources {
        limits = {
          cpu    = var.cpu
          memory = var.memory
        }
      }
    }
    
    scaling {
      min_instance_count = var.min_instances
      max_instance_count = var.max_instances
    }
    
    vpc_access {
      connector = var.vpc_connector_id
      egress    = "PRIVATE_RANGES_ONLY"
    }
  }
  
  traffic {
    type    = "TRAFFIC_TARGET_ALLOCATION_TYPE_LATEST"
    percent = 100
  }
}

# Service account for Cloud Run
resource "google_service_account" "cloud_run" {
  account_id   = "${var.environment}-${var.service_name}-sa"
  display_name = "Cloud Run Service Account"
}

# IAM for public access (adjust as needed)
resource "google_cloud_run_service_iam_member" "public" {
  count = var.allow_unauthenticated ? 1 : 0
  
  service  = google_cloud_run_v2_service.main.name
  location = var.region
  role     = "roles/run.invoker"
  member   = "allUsers"
}