# Simplified Cloud Run Deployment

## Architecture

This setup separates infrastructure management from application deployment:

- **Terraform**: Manages infrastructure (service accounts, VPC, IAM, etc.)
- **GitHub Actions**: Deploys the Cloud Run service using `cr.yaml`

## How It Works

### 1. Infrastructure (Terraform)

Terraform creates:
- ✅ Service Account for Cloud Run
- ✅ VPC Connector for private networking
- ✅ IAM permissions
- ✅ Workload Identity Federation for GitHub Actions
- ❌ NOT the Cloud Run service itself

### 2. Application Deployment (GitHub Actions)

GitHub Actions deploys using [`google-github-actions/deploy-cloudrun`](https://github.com/google-github-actions/deploy-cloudrun):
- Uses `cr.yaml` for service configuration
- Authenticates via Workload Identity Federation
- Can build and push Docker images
- Runs on push to main branch

## Setup Instructions

### Step 1: Apply Terraform Infrastructure

```bash
terraform apply
```

### Step 2: Get Terraform Outputs

```bash
terraform output cloud_run_config
```

### Step 3: Update cr.yaml

Update your `cr.yaml` with values from Terraform:

```yaml
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/vpc-access-connector: <VPC_CONNECTOR_FROM_TERRAFORM>
    spec:
      serviceAccountName: <SERVICE_ACCOUNT_FROM_TERRAFORM>
```

Or use the helper script:
```bash
chmod +x update-cr-yaml.sh
./update-cr-yaml.sh
```

### Step 4: Configure GitHub Secrets

Add these secrets to your GitHub repository:

- `GCP_PROJECT_ID`: Your GCP project ID
- `GCP_WIF_PROVIDER`: Workload Identity Provider (from Terraform output)
- `GCP_WIF_SERVICE_ACCOUNT`: GitHub Actions service account (from Terraform output)

```bash
# Get the values
terraform output workload_identity_provider
terraform output workload_identity_service_account
```

### Step 5: Deploy

Push to main branch and GitHub Actions will deploy:

```bash
git add cr.yaml
git commit -m "Update Cloud Run configuration"
git push origin main
```

## Manual Deployment

If you prefer to deploy manually:

```bash
# Deploy the service
gcloud run services replace cr.yaml --region=us-central1

# Allow public access (if needed)
gcloud run services add-iam-policy-binding main-app \
  --region=us-central1 \
  --member="allUsers" \
  --role="roles/run.invoker"
```

## File Structure

```
.
├── terraform/
│   ├── main.tf                 # Main Terraform config
│   ├── modules/
│   │   └── cloud-run/          # Simplified module (infra only)
│   │       ├── main.tf         # Service account & IAM
│   │       ├── variables.tf    
│   │       └── outputs.tf      
│   └── terraform.tfvars        # Your configuration
├── cr.yaml                     # Cloud Run service definition
├── .github/
│   └── workflows/
│       └── deploy-cloud-run.yml # GitHub Actions workflow
└── update-cr-yaml.sh           # Helper script
```

## Benefits of This Approach

1. **Separation of Concerns**: Infrastructure (Terraform) vs Application (GitHub Actions)
2. **Simpler**: No complex YAML parsing in Terraform
3. **Flexible**: Easy to update `cr.yaml` without running Terraform
4. **CI/CD Native**: Deployments happen through your CI/CD pipeline
5. **Version Control**: Both infrastructure and application config are versioned

## Troubleshooting

### Service account not found
Make sure to run `terraform apply` first to create the service account.

### VPC connector not found
The VPC connector must be created by Terraform before deploying Cloud Run.

### Permission denied
Check that the service account has the necessary IAM roles:
```bash
terraform output cloud_run_config
```

### First deployment fails
The IAM binding for public access might fail on first run since the service doesn't exist yet. Either:
1. Comment out the IAM binding in Terraform
2. Deploy the service first via GitHub Actions
3. Then uncomment and run `terraform apply` again

## Next Steps

1. Customize `cr.yaml` for your application
2. Set up Cloud Build for automatic Docker image builds
3. Add staging/production environments
4. Configure Cloud Monitoring alerts
5. Set up Cloud Armor for DDoS protection