apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: cerana-api
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/ingress-status: all
    run.googleapis.com/invoker-iam-disabled: 'true'
spec:
  template:
    metadata:
      labels:
        client.knative.dev/nonce: spwbcpmodz
        gcb-trigger-region: global
        managed-by: gcp-cloud-build-deploy-cloud-run
        run.googleapis.com/startupProbeType: Default
      annotations:
        autoscaling.knative.dev/maxScale: '100'
        run.googleapis.com/startup-cpu-boost: 'true'
    spec:
      containerConcurrency: 80
      timeoutSeconds: 300
      serviceAccountName: <EMAIL> # This should be a dedicated SA!
      containers:
      - name: cerana-api
        image: # Point to GAR cerana-api docker image
        ports:
        - name: http1
          containerPort: 5000
        env:
        - name: DATABASE_URL # This should be replaced with cloud-sql-proxy
          valueFrom:
            secretKeyRef:
              key: latest
              name: cerana_pg_url
        resources:
          limits:
            cpu: 1000m
            memory: 512Mi
        startupProbe:
          timeoutSeconds: 240
          periodSeconds: 240
          failureThreshold: 1
          tcpSocket:
            port: 5000
  traffic:
  - percent: 100
    latestRevision: true