# outputs.tf
#output "gke_cluster_name" {
#  description = "GKE cluster name"
#  value       = module.gke.cluster_name
#}

#output "gke_cluster_endpoint" {
#  description = "GKE cluster endpoint"
#  value       = module.gke.cluster_endpoint
#  sensitive   = true
#}

output "artifact_registry_repositories" {
  description = "Artifact Registry repository URLs"
  value       = module.artifact_registry.repository_urls
}

output "cloudsql_instance_name" {
  description = "CloudSQL instance name"
  value       = module.cloudsql.instance_name
}

output "cloudsql_connection_name" {
  description = "CloudSQL connection name"
  value       = module.cloudsql.connection_name
}

output "workload_identity_provider" {
  description = "Workload Identity Provider for GitHub Actions"
  value       = module.workload_identity.provider_name
}

output "workload_identity_service_accounts" {
  description = "Service Accounts for GitHub Actions by repository"
  value       = module.workload_identity.service_account_emails
}

output "vpc_network_name" {
  description = "VPC network name"
  value       = google_compute_network.main.name
}

output "cloud_run_service_account" {
  description = "Service account email for Cloud Run (cerana-api)"
  value       = google_service_account.cerana_api.email
}

output "talent_api_cloud_run_service_account" {
  description = "Service account email for Cloud Run (talent-api)"
  value       = google_service_account.talent_api.email
}

output "cloud_run_config" {
  description = "Configuration values for Cloud Run deployment (cerana-api)"
  value = {
    service_account_email = google_service_account.cerana_api.email
    vpc_connector        = google_vpc_access_connector.connector.id
    vpc_connector_name   = google_vpc_access_connector.connector.name
    project_id           = var.project_id
    region               = var.region
  }
}

output "talent_api_cloud_run_config" {
  description = "Configuration values for Cloud Run deployment (talent-api)"
  value = {
    service_account_email = google_service_account.talent_api.email
    vpc_connector        = google_vpc_access_connector.connector.id
    vpc_connector_name   = google_vpc_access_connector.connector.name
    project_id           = var.project_id
    region               = var.region
  }
}

output "cr_yaml_snippet" {
  description = "Ready-to-use snippet for cr.yaml (cerana-api)"
  value = <<-EOT
    # Add to your cr.yaml:
    spec:
      template:
        metadata:
          annotations:
            run.googleapis.com/vpc-access-connector: ${google_vpc_access_connector.connector.id}
            run.googleapis.com/vpc-access-egress: PRIVATE_RANGES_ONLY
        spec:
          serviceAccountName: ${module.cloud_run.service_account_email}
  EOT
}

output "talent_api_cr_yaml_snippet" {
  description = "Ready-to-use snippet for cr-talent.yaml (talent-api)"
  value = <<-EOT
    # Add to your cr-talent.yaml:
    spec:
      template:
        metadata:
          annotations:
            run.googleapis.com/vpc-access-connector: ${google_vpc_access_connector.connector.id}
            run.googleapis.com/vpc-access-egress: PRIVATE_RANGES_ONLY
        spec:
          serviceAccountName: ${google_service_account.talent_api.email}
  EOT
}

output "pubsub_topic_names" {
  description = "Pub/Sub topic names for talent-api"
  value       = module.pubsub.topic_names
}

output "pubsub_topic_ids" {
  description = "Pub/Sub topic full resource IDs for talent-api"
  value       = module.pubsub.topic_ids
}

output "storage_bucket_names" {
  description = "GCS bucket names"
  value       = module.storage.bucket_names
}

output "storage_bucket_urls" {
  description = "GCS bucket URLs"
  value       = module.storage.bucket_urls
}

output "talent_api_secret_ids" {
  description = "Secret Manager secret IDs for talent-api"
  value       = module.talent_api_secrets.secret_ids
}

output "pubsub_subscription_names" {
  description = "Pub/Sub subscription names for talent-api"
  value       = module.pubsub.subscription_names
}

output "pubsub_subscription_ids" {
  description = "Pub/Sub subscription full resource IDs for talent-api"
  value       = module.pubsub.subscription_ids
}

output "github_actions_secrets" {
  description = "Values to add as GitHub Actions secrets for each repository"
  value = <<-EOT
    Add these as GitHub Secrets:

    For infrastructure repository:
    - GCP_PROJECT_ID: ${var.project_id}
    - GCP_WIF_PROVIDER: ${module.workload_identity.provider_name}
    - GCP_WIF_SERVICE_ACCOUNT: ${module.workload_identity.service_account_emails["infrastructure"]}

    For cerana-api repository:
    - GCP_PROJECT_ID: ${var.project_id}
    - GCP_WIF_PROVIDER: ${module.workload_identity.provider_name}
    - GCP_WIF_SERVICE_ACCOUNT: ${module.workload_identity.service_account_emails["cerana-api"]}

    For talent-api repository:
    - GCP_PROJECT_ID: ${var.project_id}
    - GCP_WIF_PROVIDER: ${module.workload_identity.provider_name}
    - GCP_WIF_SERVICE_ACCOUNT: ${module.workload_identity.service_account_emails["talent-api"]}
  EOT
}