# GCP Terraform Project Setup Guide

## Quick Start
# Authenticate
```
gcloud auth application-default login
```

## Prerequisites for macOS

1. **Install Terraform**
```bash
# Using Homebrew
brew tap hashicorp/tap
brew install hashicorp/tap/terraform

# Verify installation
terraform --version
```

2. **Install Google Cloud SDK**
```bash
# Using Homebrew
brew install --cask google-cloud-sdk

# Initialize gcloud
gcloud init

# Authenticate
gcloud auth application-default login
```

3. **Set up your GCP Project**
```bash
# Set your project ID
export PROJECT_ID="your-project-id"
export REGION="us-central1"
export ZONE="us-central1-a"

# Set the project
gcloud config set project $PROJECT_ID

# Enable required APIs
gcloud services enable compute.googleapis.com
gcloud services enable container.googleapis.com
gcloud services enable artifactregistry.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable sqladmin.googleapis.com
gcloud services enable iam.googleapis.com
gcloud services enable iamcredentials.googleapis.com
gcloud services enable cloudresourcemanager.googleapis.com
gcloud services enable sts.googleapis.com
```

## Step 1: Create Terraform State Bucket

```bash
# Create the bucket for Terraform state
export BUCKET_NAME="${PROJECT_ID}-terraform-state"

# Create the bucket
gsutil mb -p $PROJECT_ID -l $REGION gs://$BUCKET_NAME/

# Enable versioning on the bucket
gsutil versioning set on gs://$BUCKET_NAME/

# Enable uniform bucket-level access
gsutil uniformbucketlevelaccess set on gs://$BUCKET_NAME/
```

## Step 2: Project Structure

Create the following directory structure:

```
dev/
├── main.tf
├── variables.tf
├── outputs.tf
├── versions.tf
├── backend.tf
├── terraform.tfvars
├── modules/
│   ├── gke/
│   │   ├── main.tf
│   │   ├── variables.tf
│   │   └── outputs.tf
│   ├── artifact-registry/
│   │   ├── main.tf
│   │   ├── variables.tf
│   │   └── outputs.tf
│   ├── cloud-run/
│   │   ├── main.tf
│   │   ├── variables.tf
│   │   └── outputs.tf
│   ├── cloudsql/
│   │   ├── main.tf
│   │   ├── variables.tf
│   │   └── outputs.tf
│   └── workload-identity/
│       ├── main.tf
│       ├── variables.tf
│       └── outputs.tf
```

## Step 3: Initialize Terraform

```bash
cd dev

# Initialize Terraform with the backend
terraform init

# Format all files
terraform fmt -recursive

# Validate the configuration
terraform validate
```

## Step 4: Deploy Infrastructure

```bash
# Review the plan
terraform plan

# Apply the configuration
terraform apply

# To apply without prompt (use with caution)
terraform apply -auto-approve
```

## Step 5: Configure GitHub Actions

After Terraform creates the Workload Identity resources, configure your GitHub repository:

1. **Add GitHub Secrets:**
   - `GCP_PROJECT_ID`: Your GCP project ID
   - `GCP_WIF_PROVIDER`: The Workload Identity Provider name (output from Terraform)
   - `GCP_WIF_SERVICE_ACCOUNT`: The service account email (output from Terraform)

2. **Example GitHub Actions Workflow:**

```yaml
name: Deploy to GCP

on:
  push:
    branches: [main]

permissions:
  contents: read
  id-token: write

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - id: auth
      uses: google-github-actions/auth@v2
      with:
        workload_identity_provider: ${{ secrets.GCP_WIF_PROVIDER }}
        service_account: ${{ secrets.GCP_WIF_SERVICE_ACCOUNT }}
    
    - name: Set up Cloud SDK
      uses: google-github-actions/setup-gcloud@v2
    
    - name: Deploy to Cloud Run
      run: |
        gcloud run deploy my-service \
          --image gcr.io/${{ secrets.GCP_PROJECT_ID }}/my-app:latest \
          --region us-central1
```

## Step 6: Access Resources

```bash
# Get GKE credentials
gcloud container clusters get-credentials main-cluster --zone us-central1-a

# Verify kubectl access
kubectl get nodes

# List Artifact Registry repositories
gcloud artifacts repositories list

# List Cloud Run services
gcloud run services list --region us-central1

# Connect to CloudSQL
gcloud sql connect main-postgres-instance --user=postgres
```

## Useful Commands

```bash
# Show current state
terraform state list

# Show specific resource
terraform state show module.gke.google_container_cluster.primary

# Destroy specific module
terraform destroy -target=module.cloud_run

# Destroy everything
terraform destroy

# Import existing resources
terraform import module.gke.google_container_cluster.primary projects/${PROJECT_ID}/locations/${ZONE}/clusters/main-cluster
```

## Troubleshooting

1. **API not enabled error:**
   - Run the enable services commands from the prerequisites section

2. **Permission denied:**
   - Ensure your user has Owner or Editor role:
   ```bash
   gcloud projects add-iam-policy-binding $PROJECT_ID \
     --member="user:<EMAIL>" \
     --role="roles/owner"
   ```

3. **State lock issues:**
   - Break the lock (use with caution):
   ```bash
   terraform force-unlock <lock-id>
   ```

4. **Resource quota exceeded:**
   - Check and request quota increases:
   ```bash
   gcloud compute project-info describe --project=$PROJECT_ID
   ```

## Best Practices Applied

1. **Modularity**: Each component is in its own module for reusability
2. **State Management**: Remote state in GCS with versioning enabled
3. **Security**: 
   - Private GKE cluster with authorized networks
   - Workload Identity for pod authentication
   - CloudSQL with private IP
   - Least privilege IAM roles
4. **Naming Conventions**: Consistent resource naming with environment prefixes
5. **Tagging**: All resources tagged with environment and managed-by labels
6. **Documentation**: Inline comments and variable descriptions
7. **Version Pinning**: Terraform and provider versions locked
