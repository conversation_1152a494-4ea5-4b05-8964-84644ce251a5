import React, { useState, useEffect } from 'react';
import { ChevronRight, Users, Target, Sparkles, Brain, Clock, CheckCircle, AlertCircle, Loader, ArrowRight } from 'lucide-react';

const CeranaLabsMVP = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState({
    basicInfo: {},
    companyInfo: {},
    fundingGoals: {},
    talentNeeds: {}
  });
  const [aiProcessing, setAiProcessing] = useState(false);
  const [aiInsights, setAiInsights] = useState(null);
  const [showResults, setShowResults] = useState(false);

  const steps = [
    { id: 'basic', title: 'Basic Info', icon: Users },
    { id: 'company', title: 'Company Details', icon: Target },
    { id: 'funding', title: 'Funding Goals', icon: Sparkles },
    { id: 'talent', title: 'Talent Needs', icon: Brain },
    { id: 'processing', title: 'AI Analysis', icon: Clock }
  ];

  const simulateAIProcessing = async (data) => {
    setAiProcessing(true);
    
    // Simulate AI processing with realistic delays
    const processingSteps = [
      "Analyzing company profile...",
      "Matching with investor database...",
      "Identifying ideal talent profiles...",
      "Calculating readiness scores...",
      "Generating recommendations..."
    ];
    
    for (let i = 0; i < processingSteps.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 1200));
    }

    // Generate realistic AI insights based on form data
    const insights = {
      readinessScore: Math.floor(Math.random() * 30) + 70, // 70-100%
      topInvestors: [
        { name: "Sequoia Capital", match: "95%", reason: "Perfect fit for your AI/SaaS focus" },
        { name: "Andreessen Horowitz", match: "89%", reason: "Strong track record in B2B platforms" },
        { name: "Index Ventures", match: "84%", reason: "Active in European expansion" }
      ],
      talentMatches: [
        { name: "Sarah Chen", role: "CTO", match: "92%", experience: "Ex-Google, 8 years AI/ML" },
        { name: "Marcus Weber", role: "Head of Engineering", match: "87%", experience: "Ex-Stripe, scaling platforms" },
        { name: "Priya Patel", role: "VP Product", match: "85%", experience: "Ex-Figma, B2B product expert" }
      ],
      recommendations: [
        "Consider refining your TAM analysis with more granular market segmentation",
        "Your technical team composition is strong for early-stage development",
        "Revenue projections align well with similar B2B SaaS companies at your stage"
      ],
      nextSteps: [
        "Schedule intro calls with top 3 investor matches",
        "Review talent profiles and initiate conversations",
        "Complete pitch deck optimization based on investor preferences"
      ]
    };

    setAiInsights(insights);
    setAiProcessing(false);
    setShowResults(true);
  };

  const BasicInfoForm = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-2xl font-bold text-gray-900 mb-2">Let's start with the basics</h3>
        <p className="text-gray-600">Tell us about yourself and your founding journey</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
          <input 
            type="text" 
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            placeholder="John Doe"
            onChange={(e) => setFormData(prev => ({
              ...prev,
              basicInfo: { ...prev.basicInfo, name: e.target.value }
            }))}
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
          <input 
            type="email" 
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            placeholder="<EMAIL>"
            onChange={(e) => setFormData(prev => ({
              ...prev,
              basicInfo: { ...prev.basicInfo, email: e.target.value }
            }))}
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Role</label>
          <select 
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            onChange={(e) => setFormData(prev => ({
              ...prev,
              basicInfo: { ...prev.basicInfo, role: e.target.value }
            }))}
          >
            <option value="">Select your role</option>
            <option value="ceo">CEO/Co-founder</option>
            <option value="cto">CTO/Co-founder</option>
            <option value="founder">Founder</option>
            <option value="other">Other</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Previous Experience</label>
          <select 
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            onChange={(e) => setFormData(prev => ({
              ...prev,
              basicInfo: { ...prev.basicInfo, experience: e.target.value }
            }))}
          >
            <option value="">Select experience level</option>
            <option value="first-time">First-time founder</option>
            <option value="repeat">Repeat founder</option>
            <option value="executive">Former C-level executive</option>
            <option value="technical">Senior technical role</option>
          </select>
        </div>
      </div>
    </div>
  );

  const CompanyInfoForm = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-2xl font-bold text-gray-900 mb-2">Tell us about your company</h3>
        <p className="text-gray-600">Help us understand your business and market</p>
      </div>
      
      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Company Name</label>
          <input 
            type="text" 
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            placeholder="Your company name"
            onChange={(e) => setFormData(prev => ({
              ...prev,
              companyInfo: { ...prev.companyInfo, name: e.target.value }
            }))}
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Industry</label>
          <select 
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            onChange={(e) => setFormData(prev => ({
              ...prev,
              companyInfo: { ...prev.companyInfo, industry: e.target.value }
            }))}
          >
            <option value="">Select industry</option>
            <option value="ai-ml">AI/Machine Learning</option>
            <option value="fintech">FinTech</option>
            <option value="saas">SaaS/Enterprise Software</option>
            <option value="healthcare">Healthcare/Biotech</option>
            <option value="consumer">Consumer/Marketplace</option>
            <option value="other">Other</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Stage</label>
          <select 
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            onChange={(e) => setFormData(prev => ({
              ...prev,
              companyInfo: { ...prev.companyInfo, stage: e.target.value }
            }))}
          >
            <option value="">Select current stage</option>
            <option value="idea">Idea/Concept</option>
            <option value="mvp">MVP Development</option>
            <option value="pilot">Pilot Customers</option>
            <option value="revenue">Generating Revenue</option>
            <option value="scaling">Scaling</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">One-line Description</label>
          <textarea 
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            rows="3"
            placeholder="Describe your company in one compelling sentence..."
            onChange={(e) => setFormData(prev => ({
              ...prev,
              companyInfo: { ...prev.companyInfo, description: e.target.value }
            }))}
          />
        </div>
      </div>
    </div>
  );

  const FundingGoalsForm = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-2xl font-bold text-gray-900 mb-2">Funding objectives</h3>
        <p className="text-gray-600">Let's understand your fundraising goals and timeline</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Funding Stage</label>
          <select 
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            onChange={(e) => setFormData(prev => ({
              ...prev,
              fundingGoals: { ...prev.fundingGoals, stage: e.target.value }
            }))}
          >
            <option value="">Select funding stage</option>
            <option value="pre-seed">Pre-seed</option>
            <option value="seed">Seed</option>
            <option value="series-a">Series A</option>
            <option value="series-b">Series B+</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Target Amount</label>
          <select 
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            onChange={(e) => setFormData(prev => ({
              ...prev,
              fundingGoals: { ...prev.fundingGoals, amount: e.target.value }
            }))}
          >
            <option value="">Select target amount</option>
            <option value="500k">$500K - $1M</option>
            <option value="1-3m">$1M - $3M</option>
            <option value="3-5m">$3M - $5M</option>
            <option value="5m+">$5M+</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Timeline</label>
          <select 
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            onChange={(e) => setFormData(prev => ({
              ...prev,
              fundingGoals: { ...prev.fundingGoals, timeline: e.target.value }
            }))}
          >
            <option value="">When do you need funding?</option>
            <option value="immediate">Immediately</option>
            <option value="3-months">Next 3 months</option>
            <option value="6-months">Next 6 months</option>
            <option value="planning">Just planning ahead</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Use of Funds</label>
          <select 
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            onChange={(e) => setFormData(prev => ({
              ...prev,
              fundingGoals: { ...prev.fundingGoals, useOfFunds: e.target.value }
            }))}
          >
            <option value="">Primary use of funds</option>
            <option value="hiring">Team building/hiring</option>
            <option value="product">Product development</option>
            <option value="marketing">Marketing/growth</option>
            <option value="operations">Operations/scaling</option>
          </select>
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Investor Preferences</label>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {['Tier 1 VCs', 'Strategic VCs', 'Angel Investors', 'Sector Specialists', 'Geographic Focus', 'Hands-on Support'].map((pref) => (
            <label key={pref} className="flex items-center space-x-2">
              <input type="checkbox" className="text-orange-500 focus:ring-orange-500" />
              <span className="text-sm text-gray-700">{pref}</span>
            </label>
          ))}
        </div>
      </div>
    </div>
  );

  const TalentNeedsForm = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-2xl font-bold text-gray-900 mb-2">Talent requirements</h3>
        <p className="text-gray-600">What key roles do you need to fill to achieve your goals?</p>
      </div>
      
      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Immediate Hiring Needs</label>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {['CTO/Technical Co-founder', 'Head of Engineering', 'Senior Engineer', 'Product Manager', 'Head of Sales', 'Marketing Lead', 'Operations Manager', 'Designer'].map((role) => (
              <label key={role} className="flex items-center space-x-2">
                <input type="checkbox" className="text-orange-500 focus:ring-orange-500" />
                <span className="text-sm text-gray-700">{role}</span>
              </label>
            ))}
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Team Size Goal</label>
            <select 
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              onChange={(e) => setFormData(prev => ({
                ...prev,
                talentNeeds: { ...prev.talentNeeds, teamSize: e.target.value }
              }))}
            >
              <option value="">Target team size in 12 months</option>
              <option value="5-10">5-10 people</option>
              <option value="10-20">10-20 people</option>
              <option value="20-50">20-50 people</option>
              <option value="50+">50+ people</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Hiring Timeline</label>
            <select 
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              onChange={(e) => setFormData(prev => ({
                ...prev,
                talentNeeds: { ...prev.talentNeeds, timeline: e.target.value }
              }))}
            >
              <option value="">When do you need to hire?</option>
              <option value="immediate">Immediately</option>
              <option value="1-month">Next month</option>
              <option value="3-months">Next 3 months</option>
              <option value="6-months">Next 6 months</option>
            </select>
          </div>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Key Challenges</label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {['Finding technical talent', 'Competing with big tech salaries', 'Remote vs. on-site', 'Cultural fit assessment', 'Equity negotiations', 'Geographic constraints'].map((challenge) => (
              <label key={challenge} className="flex items-center space-x-2">
                <input type="checkbox" className="text-orange-500 focus:ring-orange-500" />
                <span className="text-sm text-gray-700">{challenge}</span>
              </label>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const AIProcessingStep = () => (
    <div className="text-center space-y-8">
      <div>
        <h3 className="text-2xl font-bold text-gray-900 mb-2">AI-Powered Analysis in Progress</h3>
        <p className="text-gray-600">Our AI agents are analyzing your profile and matching you with the perfect investors and talent</p>
      </div>
      
      <div className="bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg p-8">
        <div className="flex flex-col items-center space-y-6">
          <div className="relative">
            <div className="w-20 h-20 border-4 border-orange-200 rounded-full animate-spin border-t-orange-500"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <Brain className="w-8 h-8 text-orange-500" />
            </div>
          </div>
          
          <div className="space-y-3 text-center">
            <div className="text-lg font-semibold text-gray-800">
              {aiProcessing ? "Processing your data..." : "Analysis complete!"}
            </div>
            <div className="text-sm text-gray-600">
              Powered by Anthropic's Claude AI
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 w-full max-w-md">
            {[
              "Analyzing company profile",
              "Matching with investors",
              "Identifying talent matches"
            ].map((step, index) => (
              <div key={step} className="flex items-center space-x-2 bg-white rounded-lg p-3">
                {!aiProcessing ? (
                  <CheckCircle className="w-5 h-5 text-green-500" />
                ) : (
                  <Loader className="w-5 h-5 text-orange-500 animate-spin" />
                )}
                <span className="text-sm text-gray-700">{step}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const ResultsView = () => (
    <div className="space-y-8">
      <div className="text-center">
        <h3 className="text-2xl font-bold text-gray-900 mb-2">Your Personalized Matches</h3>
        <p className="text-gray-600">AI-powered recommendations tailored to your company profile</p>
      </div>
      
      {/* Readiness Score */}
      <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-lg font-semibold text-gray-800">Funding Readiness Score</h4>
            <p className="text-sm text-gray-600">Based on our proprietary algorithm</p>
          </div>
          <div className="text-right">
            <div className="text-3xl font-bold text-green-600">{aiInsights?.readinessScore}%</div>
            <div className="text-sm text-gray-600">Ready to pitch</div>
          </div>
        </div>
      </div>
      
      {/* Top Investor Matches */}
      <div>
        <h4 className="text-lg font-semibold text-gray-800 mb-4">Top Investor Matches</h4>
        <div className="space-y-3">
          {aiInsights?.topInvestors.map((investor, index) => (
            <div key={index} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="font-semibold text-gray-800">{investor.name}</div>
                  <div className="text-sm text-gray-600">{investor.reason}</div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-semibold text-orange-600">{investor.match}</div>
                  <div className="text-sm text-gray-500">match</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      {/* Top Talent Matches */}
      <div>
        <h4 className="text-lg font-semibold text-gray-800 mb-4">Top Talent Matches</h4>
        <div className="space-y-3">
          {aiInsights?.talentMatches.map((talent, index) => (
            <div key={index} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="font-semibold text-gray-800">{talent.name}</div>
                  <div className="text-sm text-gray-600">{talent.role} • {talent.experience}</div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-semibold text-blue-600">{talent.match}</div>
                  <div className="text-sm text-gray-500">match</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      {/* Next Steps */}
      <div className="bg-blue-50 rounded-lg p-6">
        <h4 className="text-lg font-semibold text-gray-800 mb-4">Recommended Next Steps</h4>
        <div className="space-y-2">
          {aiInsights?.nextSteps.map((step, index) => (
            <div key={index} className="flex items-center space-x-3">
              <ArrowRight className="w-5 h-5 text-blue-500" />
              <span className="text-gray-700">{step}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
      if (currentStep === 3) { // After talent needs form
        simulateAIProcessing(formData);
      }
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0: return <BasicInfoForm />;
      case 1: return <CompanyInfoForm />;
      case 2: return <FundingGoalsForm />;
      case 3: return <TalentNeedsForm />;
      case 4: return showResults ? <ResultsView /> : <AIProcessingStep />;
      default: return <BasicInfoForm />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-black text-white">
        <div className="max-w-6xl mx-auto px-6 py-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">C</span>
            </div>
            <div>
              <div className="font-bold text-lg">cerana labs</div>
              <div className="text-sm text-gray-300">AI-Driven Serendipity</div>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="bg-white border-b">
        <div className="max-w-4xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = index === currentStep;
              const isCompleted = index < currentStep || showResults;
              
              return (
                <div key={step.id} className="flex items-center">
                  <div className={`flex items-center space-x-2 ${
                    isActive ? 'text-orange-600' : isCompleted ? 'text-green-600' : 'text-gray-400'
                  }`}>
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      isActive ? 'bg-orange-100' : isCompleted ? 'bg-green-100' : 'bg-gray-100'
                    }`}>
                      {isCompleted && !isActive ? (
                        <CheckCircle className="w-5 h-5" />
                      ) : (
                        <Icon className="w-5 h-5" />
                      )}
                    </div>
                    <span className="text-sm font-medium hidden md:block">{step.title}</span>
                  </div>
                  {index < steps.length - 1 && (
                    <ChevronRight className="w-5 h-5 text-gray-300 mx-4" />
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-6 py-8">
        <div className="bg-white rounded-lg shadow-sm border p-8">
          {renderCurrentStep()}
          
          {!showResults && currentStep < 4 && (
            <div className="mt-8 flex justify-end">
              <button
                onClick={handleNext}
                className="bg-orange-500 text-white px-6 py-3 rounded-lg font-medium hover:bg-orange-600 transition-colors flex items-center space-x-2"
              >
                <span>{currentStep === 3 ? 'Start AI Analysis' : 'Continue'}</span>
                <ChevronRight className="w-5 h-5" />
              </button>
            </div>
          )}
          
          {showResults && (
            <div className="mt-8 flex justify-center space-x-4">
              <button className="bg-orange-500 text-white px-6 py-3 rounded-lg font-medium hover:bg-orange-600 transition-colors">
                Contact Top Matches
              </button>
              <button className="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors">
                Download Report
              </button>
            </div>
          )}
        </div>
        
        {showResults && (
          <div className="mt-6 text-center text-sm text-gray-500">
            <p>Powered by <strong>Anthropic Claude AI</strong> • Matches updated in real-time</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CeranaLabsMVP;
