:root {
    --primary: #3b82f6;
    --primary-dark: #2563eb;
    --background: #0f172a;
    --surface: #1e293b;
    --surface-light: #334155;
    --text: #f8fafc;
    --text-light: #94a3b8;
    --border: #334155;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: var(--background);
    color: var(--text);
    line-height: 1.5;
}

.app-container {
    display: flex;
    min-height: 100vh;
}

.sidebar {
    width: 280px;
    min-width: 280px;
    background: var(--surface);
    padding: 2rem;
    border-right: 1px solid var(--border);
    display: flex;
    flex-direction: column;
}

.logo {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary);
    margin-bottom: 3rem;
}

.nav-links {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex-grow: 1;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 1rem;
    text-decoration: none;
    color: var(--text-light);
    border-radius: 0.5rem;
    transition: all 0.2s;
}

.nav-link:hover, .nav-link.active {
    background: var(--surface-light);
    color: var(--text);
}

.nav-link svg {
    width: 20px;
    height: 20px;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-top: 1px solid var(--border);
    margin-top: auto;
}

.avatar {
    width: 40px;
    height: 40px;
    background: var(--primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.user-info {
    flex-grow: 1;
}

.user-name {
    font-weight: 500;
}

.user-role {
    font-size: 0.875rem;
    color: var(--text-light);
}

.main-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
    max-width: calc(100vw - 280px);
}

.content-header {
    margin-bottom: 2rem;
}

.content-header h1 {
    font-size: 2rem;
    margin-bottom: 1.5rem;
}

.filters {
    display: flex;
    gap: 0.5rem;
}

.filter-btn {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    background: var(--surface);
    border: 1px solid var(--border);
    color: var(--text-light);
    cursor: pointer;
    transition: all 0.2s;
}

.filter-btn:hover, .filter-btn.active {
    background: var(--surface-light);
    color: var(--text);
    border-color: var(--primary);
}

.cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    width: 100%;
    max-width: 100%;
}

.card {
    background: var(--surface);
    border-radius: 1rem;
    padding: 1.5rem;
    border: 1px solid var(--border);
    cursor: pointer;
    transition: all 0.2s;
}

.card:hover {
    transform: translateY(-4px);
    border-color: var(--primary);
}

.card h3 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    color: var(--text);
}

.card p {
    color: var(--text-light);
    margin-bottom: 1rem;
}

.tag {
    display: inline-block;
    background: var(--surface-light);
    color: var(--primary);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    overflow: hidden;
}

.modal-content {
    background: var(--surface);
    margin: 5% auto;
    padding: 2rem;
    border-radius: 1rem;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    position: relative;
    border: 1px solid var(--border);
    overflow-y: auto;
    scroll-behavior: smooth;
}

.close {
    position: absolute;
    right: 1.5rem;
    top: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-light);
}

.hidden {
    display: none;
}

.subtitle {
    color: var(--text-light);
    margin-bottom: 2rem;
}

.matches-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    width: 100%;
    max-width: 100%;
}

.match-card {
    background: var(--surface);
    border-radius: 1rem;
    padding: 2rem;
    border: 1px solid var(--border);
    position: relative;
}

.match-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary) 0%, var(--primary-dark) 100%);
    border-radius: 1rem 1rem 0 0;
}

.match-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.match-title {
    flex: 1;
}

.match-title h2 {
    color: var(--primary);
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
}

.match-title .subtitle {
    margin: 0;
    font-size: 0.875rem;
}

.match-score {
    background: var(--primary);
    color: var(--text);
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.match-score svg {
    width: 16px;
    height: 16px;
}

.match-details {
    display: grid;
    grid-template-columns: 1fr 80px 1fr;
    gap: 1rem;
    align-items: start;
}

.match-side {
    background: var(--surface-light);
    border-radius: 0.75rem;
    padding: 1.5rem;
}

.match-connector {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1rem 0;
    color: var(--primary);
    position: relative;
}

.match-connector::before,
.match-connector::after {
    content: '';
    position: absolute;
    left: 50%;
    width: 2px;
    background: var(--primary);
    transform: translateX(-50%);
}

.match-connector::before {
    top: 0;
    height: 40%;
}

.match-connector::after {
    bottom: 0;
    height: 40%;
}

.match-connector svg {
    width: 32px;
    height: 32px;
    padding: 6px;
    border-radius: 50%;
    background: var(--surface);
    border: 2px solid var(--primary);
    z-index: 1;
}

.match-side h3 {
    color: var(--text);
    margin-bottom: 1rem;
    font-size: 1.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.match-side h3 svg {
    width: 20px;
    height: 20px;
    color: var(--primary);
}

.match-side .subtitle {
    color: var(--primary);
    font-weight: 500;
    margin-bottom: 1rem;
}

.match-side .explanation {
    color: var(--text-light);
    line-height: 1.6;
    font-size: 0.9375rem;
}

.match-side .highlight {
    color: var(--text);
    font-weight: 500;
}

@media (max-width: 768px) {
    .app-container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        padding: 1rem;
    }
    
    .nav-links {
        flex-direction: row;
        justify-content: center;
    }
    
    .user-profile {
        display: none;
    }
}

@media (max-width: 768px) {
    .match-card {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .match-details {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .match-connector {
        flex-direction: row;
        padding: 0.5rem 0;
    }
    
    .match-connector::before,
    .match-connector::after {
        top: 50%;
        height: 2px;
        width: 40%;
        transform: translateY(-50%);
    }
    
    .match-connector::before {
        left: 0;
    }
    
    .match-connector::after {
        left: auto;
        right: 0;
    }
}