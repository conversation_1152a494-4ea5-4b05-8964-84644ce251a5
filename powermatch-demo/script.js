// Load and parse CSV data
async function loadCSV(url) {
    const response = await fetch(url);
    const text = await response.text();
    const lines = text.split('\n');
    const headers = lines[0].split(',');
    
    return lines.slice(1).map(line => {
        // Handle quoted values containing commas
        const values = [];
        let currentValue = '';
        let insideQuotes = false;
        
        for (let char of line) {
            if (char === '"') {
                insideQuotes = !insideQuotes;
            } else if (char === ',' && !insideQuotes) {
                values.push(currentValue.trim().replace(/^"|"$/g, ''));
                currentValue = '';
            } else {
                currentValue += char;
            }
        }
        values.push(currentValue.trim().replace(/^"|"$/g, '')); // Push the last value
        
        return headers.reduce((obj, header, index) => {
            obj[header.trim()] = values[index]?.trim();
            return obj;
        }, {});
    });
}

// Load JSON data
async function loadJSON(url) {
    const response = await fetch(url);
    return await response.json();
}

// Initialize the page
async function initialize() {
    const [founders, investors, founderIntros, vcIntros] = await Promise.all([
        loadCSV('founder.csv'),
        loadCSV('investor.csv'),
        loadJSON('founder_intros.json'),
        loadJSON('vc_intros.json')
    ]);

    setupNavigation();
    setupFilters(founders, investors, vcIntros, founderIntros);
    displayFounders(founders, vcIntros);
    displayInvestors(investors, founderIntros);
    displayPowerMatches(founders, investors, founderIntros, vcIntros);
    setupModal();
}

function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    const views = document.querySelectorAll('.view-container');
    
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            
            // Update active nav link
            navLinks.forEach(l => l.classList.remove('active'));
            link.classList.add('active');
            
            // Show corresponding view
            const viewToShow = link.dataset.view;
            views.forEach(view => {
                view.classList.toggle('hidden', 
                    !view.id.includes(viewToShow));
            });
        });
    });
}

function setupFilters(founders, investors, vcIntros, founderIntros) {
    // Setup founder filters
    const founderFilters = document.querySelectorAll('#founders-view .filter-btn');
    founderFilters.forEach(btn => {
        btn.addEventListener('click', () => {
            const filter = btn.textContent;
            
            // Update active state
            founderFilters.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            
            // Filter founders
            const container = document.getElementById('founders-container');
            container.innerHTML = '';
            
            const filteredFounders = filter === 'All' 
                ? founders 
                : founders.filter(founder => {
                    const stage = founder['Current Funding Stage'];
                    if (filter === 'Seed') return stage?.includes('Seed');
                    if (filter === 'Series A') return stage?.includes('Series A');
                    if (filter === 'Series B+') return stage?.includes('Series B') || stage?.includes('Series C');
                    return true;
                });
            
            displayFounders(filteredFounders, vcIntros);
        });
    });

    // Setup investor filters
    const investorFilters = document.querySelectorAll('#investors-view .filter-btn');
    investorFilters.forEach(btn => {
        btn.addEventListener('click', () => {
            const filter = btn.textContent;
            
            // Update active state
            investorFilters.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            
            // Filter investors
            const container = document.getElementById('investors-container');
            container.innerHTML = '';
            
            const filteredInvestors = filter === 'All' 
                ? investors 
                : investors.filter(investor => {
                    const rawFocus = investor['Sector Focus'];
                    const focusAreas = rawFocus?.toLowerCase().split(',').map(f => f.trim()) || [];
                    
                    switch(filter) {
                        case 'Fintech':
                            return focusAreas.some(focus => 
                                focus.includes('fintech') || 
                                focus.includes('financial') ||
                                focus.includes('finance'));
                        case 'SaaS':
                            return focusAreas.some(focus => 
                                focus.includes('saas') || 
                                focus.includes('software'));
                        case 'AI':
                            return focusAreas.some(focus => 
                                focus.includes('ai') || 
                                focus.includes('artificial'));
                        default:
                            return true;
                    }
                });
            
            displayInvestors(filteredInvestors, founderIntros);
        });
    });
}

function displayFounders(founders, vcIntros) {
    const container = document.getElementById('founders-container');
    
    founders.forEach(founder => {
        if (!founder['Founder(s)']) return;
        
        const matches = vcIntros.find(intro => 
            intro.founder === founder['Founder(s)']
        )?.vc_interest || [];

        const card = document.createElement('div');
        card.className = 'card';
        card.innerHTML = `
            <h3>${founder['Founder(s)']}</h3>
            <p>${founder['Startup Name']}</p>
            <p class="details">${founder['Industry']} • ${founder['Team Size']}</p>
            <div>
                <span class="tag">${founder['Current Funding Stage']}</span>
                <span class="tag">${founder['Business Model']}</span>
            </div>
        `;
        
        card.addEventListener('click', () => {
            showMatches('Recommended VCs', matches.map(match => ({
                name: match.vc_firm,
                explanation: match.explanation
            })));
        });
        
        container.appendChild(card);
    });
}

function displayInvestors(investors, founderIntros) {
    const container = document.getElementById('investors-container');
    
    investors.forEach(investor => {
        if (!investor['VC Firm']) return;
        
        const matches = founderIntros.find(intro => 
            intro.vc_firm === investor['VC Firm']
        )?.founder_interest || [];

        const card = document.createElement('div');
        card.className = 'card';
        card.innerHTML = `
            <h3>${investor['VC Firm']}</h3>
            <p>${investor['Investor Name']} - ${investor['Role']}</p>
            <div>
                <span class="tag">${investor['Stage']}</span>
                <span class="tag">${investor['Check Size']}</span>
            </div>
        `;
        
        if (matches.length > 0) {
            card.addEventListener('click', () => {
                showMatches('Recommended Founders', matches.map(match => ({
                    name: `${match.founder} (${match.startup})`,
                    explanation: match.explanation
                })));
            });
        }
        
        container.appendChild(card);
    });
}

function setupModal() {
    const modal = document.getElementById('matchModal');
    const closeBtn = document.getElementsByClassName('close')[0];
    
    // Function to toggle body scroll
    const toggleBodyScroll = (enable) => {
        document.body.style.overflow = enable ? 'auto' : 'hidden';
    };
    
    closeBtn.onclick = () => {
        modal.style.display = 'none';
        toggleBodyScroll(true);
    };
    
    window.onclick = (event) => {
        if (event.target === modal) {
            modal.style.display = 'none';
            toggleBodyScroll(true);
        }
    };
}

function showMatches(title, matches) {
    const modal = document.getElementById('matchModal');
    const modalTitle = document.getElementById('modalTitle');
    const modalContent = document.getElementById('modalContent');
    
    modalTitle.textContent = title;
    modalContent.innerHTML = matches.map(match => `
        <div style="margin-bottom: 1.5rem;">
            <h3 style="color: var(--primary); margin-bottom: 0.5rem;">${match.name}</h3>
            <p>${match.explanation}</p>
        </div>
    `).join('');
    
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden'; // Prevent body scroll when modal is open
    
    // Reset modal scroll position
    modalContent.scrollTop = 0;
}

function findPowerMatches(founderIntros, vcIntros) {
    const powerMatches = [];
    
    // Iterate through each VC firm's intros
    founderIntros.forEach(founderIntro => {
        const vcFirm = founderIntro.vc_firm;
        
        // Check each founder that's interested in this VC
        founderIntro.founder_interest.forEach(founderInterest => {
            const founder = founderInterest.founder;
            const startup = founderInterest.startup;
            
            // Find if this founder also appears in vcIntros
            const vcIntro = vcIntros.find(vi => 
                vi.founder === founder && 
                vi.startup === startup
            );
            
            // Check if the VC is also interested in this founder
            if (vcIntro) {
                const vcInterest = vcIntro.vc_interest.find(vi => 
                    vi.vc_firm === vcFirm
                );
                
                // If there's mutual interest, create a power match
                if (vcInterest) {
                    powerMatches.push({
                        founder: {
                            name: founder,
                            startup: startup,
                            explanation: founderInterest.explanation
                        },
                        investor: {
                            name: vcFirm,
                            explanation: vcInterest.explanation
                        }
                    });
                }
            }
        });
    });
    
    return powerMatches;
}

function displayPowerMatches(founders, investors, founderIntros, vcIntros) {
    const container = document.getElementById('power-matches-container');
    const powerMatches = findPowerMatches(founderIntros, vcIntros);
    
    if (powerMatches.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <p>No power matches found yet. Keep exploring!</p>
            </div>
        `;
        return;
    }
    
    powerMatches.forEach(match => {
        const matchCard = document.createElement('div');
        matchCard.className = 'match-card';
        
        // Find additional info about the founder and investor
        const founderInfo = founders.find(f => f['Founder(s)'] === match.founder.name);
        const investorInfo = investors.find(i => i['VC Firm'] === match.investor.name);
        
        matchCard.innerHTML = `
            <div class="match-header">
                <div class="match-title">
                    <h2>Power Match Found!</h2>
                    <p class="subtitle">Mutual interest detected between founder and investor</p>
                </div>
                <div class="match-score">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                        <path d="M12 22v-8"></path>
                        <path d="M12 14l7-4"></path>
                        <path d="M12 14l-7-4"></path>
                    </svg>
                    Perfect Match
                </div>
            </div>
            <div class="match-details">
                <div class="match-side founder-side">
                    <h3>
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                        </svg>
                        ${match.founder.name}
                    </h3>
                    <p class="subtitle">${match.founder.startup}</p>
                    <p class="explanation">
                        ${founderInfo ? `<span class="highlight">${founderInfo['Industry']} • ${founderInfo['Current Funding Stage']} • ${founderInfo['Team Size']}</span><br><br>` : ''}
                        ${match.founder.explanation}
                    </p>
                </div>
                
                <div class="match-connector">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                        <path d="M12 22v-8"></path>
                        <path d="M12 14l7-4"></path>
                        <path d="M12 14l-7-4"></path>
                    </svg>
                </div>
                
                <div class="match-side investor-side">
                    <h3>
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M12 2v20M2 12h20M12 2l4 4M12 2l-4 4"></path>
                        </svg>
                        ${match.investor.name}
                    </h3>
                    <p class="subtitle">${investorInfo ? `${investorInfo['Investor Name']} - ${investorInfo['Role']}` : ''}</p>
                    <p class="explanation">
                        ${investorInfo ? `<span class="highlight">${investorInfo['Stage']} • ${investorInfo['Check Size']}</span><br><br>` : ''}
                        ${match.investor.explanation}
                    </p>
                </div>
            </div>
        `;
        
        container.appendChild(matchCard);
    });
}

// Start the application
initialize().catch(console.error);