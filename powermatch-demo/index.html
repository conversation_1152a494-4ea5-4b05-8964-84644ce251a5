<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cerana | Dashboard</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="app-container">
        <nav class="sidebar">
            <div class="logo">Cerana</div>
            <div class="nav-links">
                <a href="#" class="nav-link" data-view="founders">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                        <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                    </svg>
                    Founders
                </a>
                <a href="#" class="nav-link" data-view="investors">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12 2v20M2 12h20M12 2l4 4M12 2l-4 4M12 22l4-4M12 22l-4-4M2 12l4 4M2 12l4-4M22 12l-4 4M22 12l-4-4"></path>
                    </svg>
                    Investors
                </a>
                <a href="#" class="nav-link" data-view="power-matches">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                        <path d="M12 22v-8"></path>
                        <path d="M12 14l-7-4"></path>
                        <path d="M12 14l7-4"></path>
                    </svg>
                    Power Matches
                </a>
            </div>
            <div class="user-profile">
                <div class="avatar">JS</div>
                <div class="user-info">
                    <div class="user-name">John Smith</div>
                    <div class="user-role">Founder</div>
                </div>
            </div>
        </nav>

        <main class="main-content">
            <div class="view-container" id="founders-view">
                <header class="content-header">
                    <h1>Founders</h1>
                    <div class="filters">
                        <button class="filter-btn active">All</button>
                        <button class="filter-btn">Seed</button>
                        <button class="filter-btn">Series A</button>
                        <button class="filter-btn">Series B+</button>
                    </div>
                </header>
                <div class="cards-container" id="founders-container"></div>
            </div>

            <div class="view-container hidden" id="investors-view">
                <header class="content-header">
                    <h1>Investors</h1>
                    <div class="filters">
                        <button class="filter-btn active">All</button>
                        <button class="filter-btn">SaaS</button>
                        <button class="filter-btn">AI</button>
                        <button class="filter-btn">Fintech</button>
                    </div>
                </header>
                <div class="cards-container" id="investors-container"></div>
            </div>

            <div class="view-container hidden" id="power-matches-view">
                <header class="content-header">
                    <h1>Power Matches</h1>
                    <p class="subtitle">Mutual matches between founders and investors</p>
                </header>
                <div class="matches-container" id="power-matches-container"></div>
            </div>
        </main>
    </div>

    <div id="matchModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2 id="modalTitle">Matches</h2>
            <div id="modalContent"></div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>